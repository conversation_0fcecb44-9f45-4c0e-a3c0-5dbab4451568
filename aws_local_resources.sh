#!/bin/bash
echo 'CREATING key-assist bucket'
awslocal s3api create-bucket \
    --bucket key-assist-local \

echo 'WAIT exists key-assist bucket'
awslocal s3api wait bucket-exists \
    --bucket key-assist-local \

echo 'ENABLING versioning in key-assist bucket'
awslocal s3api put-bucket-versioning \
    --bucket key-assist-local \
    --versioning-configuration Status=Enabled \

echo 'CREATING dynamodb table sessions'
awslocal dynamodb create-table \
    --table-name key-assist_sessions_local \
    --attribute-definitions \
        AttributeName=PK,AttributeType=S \
        AttributeName=SK,AttributeType=S \
        AttributeName=UPDATE_AT,AttributeType=S \
    --key-schema \
        AttributeName=PK,KeyType=HASH \
        AttributeName=SK,KeyType=RANGE \
    --provisioned-throughput \
        ReadCapacityUnits=1,WriteCapacityUnits=1 \
    --local-secondary-indexes \
        "[{\"IndexName\": \"UPDATE_AT-index\",
              \"KeySchema\":[{\"AttributeName\":\"PK\",\"KeyType\":\"HASH\"},
                            {\"AttributeName\":\"UPDATE_AT\",\"KeyType\":\"RANGE\"}],
              \"Projection\":{\"ProjectionType\":\"ALL\"}}]"