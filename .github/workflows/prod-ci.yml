name: Production Continuous Integration

on:
  pull_request:
    types:
      - labeled
      - opened
      - synchronize
      - reopened
    branches:
      - "master"

jobs:
  merge-staging:
    name: <PERSON><PERSON> Develop
    uses: unlockre/github-resources/.github/workflows/merge-environment.yml@main
    secrets: inherit
    with:
      environment-branch: develop
      only-merge-environment: true