name: Test

on:
  push:
    branches:
      - master
      - staging
      - develop
  pull_request:
    branches:
      - master
      - staging
      - develop

jobs:
  run-tests:
    timeout-minutes: 10
    runs-on: ubuntu-latest

    steps:

      - uses: actions/checkout@v2

      - name: Start docker compose test
        run: |
          export GH_TOKEN=${{ secrets.GH_TOKEN }}
          export GH_USERNAME=${{ secrets.GH_USERNAME }}
          docker compose -f docker-compose-test.yml up --exit-code-from test test && docker compose -f docker-compose-test.yml rm -fsv
