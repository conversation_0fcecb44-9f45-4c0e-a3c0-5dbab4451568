FROM public.ecr.aws/docker/library/amazoncorretto:17-alpine AS builder

# Dependencies
RUN apk add --no-cache jq curl
RUN apk upgrade --no-cache
RUN apk upgrade --no-cache amazon-corretto-17

# Initialize the source folder
RUN mkdir -p /code
WORKDIR /code/

# Github Credentials
ARG GH_USERNAME
ARG GH_TOKEN

# Only download dependencies (cached layer)
COPY gradlew build.gradle.kts gradle.properties settings.gradle.kts /code/
COPY gradle/ /code/gradle/
WORKDIR /code/

# Eat the expected build failure since no source code has been copied yet

RUN echo "Downloading dependencies..."
RUN export GH_USERNAME=${GH_USERNAME}
RUN export GH_TOKEN=${GH_TOKEN}
RUN ./gradlew clean build --no-daemon >/dev/null 2>&1 || true

# Datadog APM agent
RUN wget --no-check-certificate -O /code/dd-java-agent.jar https://dtdg.co/latest-java-tracer

# Copy the rest of the code
COPY . /code/

RUN echo "Replacing log4j2.xml"
RUN cp -f /code/application/src/main/resources/log4j2.docker.xml /code/application/src/main/resources/log4j2.xml

# The actual build
RUN echo "Building the app..."
RUN export GH_USERNAME=${GH_USERNAME}
RUN export GH_TOKEN=${GH_TOKEN}
RUN ./gradlew build -x test --no-daemon

FROM public.ecr.aws/docker/library/amazoncorretto:17-alpine

RUN apk add --no-cache jq curl
RUN apk upgrade --no-cache
RUN apk upgrade amazon-corretto-17

RUN mkdir -p /code
COPY --from=builder /code/application/build/libs/service.jar /code/
COPY --from=builder /code/dd-java-agent.jar /code/
COPY --from=builder /code/scripts/startup.sh /code/

ENTRYPOINT [ "/code/startup.sh" ]
