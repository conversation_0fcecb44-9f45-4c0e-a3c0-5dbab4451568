import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

val javalinVersion: String by rootProject
val kommonsMapperVersion: String by rootProject
val kommonsHttpVersion: String by rootProject
val unirestVersion: String by rootProject
val awsSdkVersion: String by rootProject
val openAIVersion: String by rootProject
val ktorClientJavaVersion: String by rootProject
val caffeineVersion: String by rootProject
val kommonsAwsVersion: String by rootProject
val swaggerParserVersion: String by rootProject
val jettyVersion: String by rootProject
val kommonsAuthVersion: String by rootProject

plugins {
    kotlin("jvm")
    kotlin("kapt")
}

dependencies {
    implementation(project(":core"))

    // Javalin
    implementation("io.javalin:javalin:$javalinVersion")
    kapt("io.javalin.community.openapi:openapi-annotation-processor:$javalinVersion")
    implementation("io.javalin.community.openapi:javalin-openapi-plugin:$javalinVersion")

    implementation("io.swagger.parser.v3:swagger-parser:$swaggerParserVersion")
    implementation("org.eclipse.jetty:jetty-server:$jettyVersion")


    implementation("com.keyway:kommons-aws:$kommonsAwsVersion")

    // Mapper
    implementation("com.keyway:kommons-mapper:$kommonsMapperVersion") {
        exclude(group = "com.google.code.gson")
    }

    // Rest
    implementation("com.konghq:unirest-java:$unirestVersion")
    implementation("com.keyway:kommons-http:$kommonsHttpVersion"){
        exclude(group = "org.apache.logging.log4j")
    }

    // S3
    implementation("software.amazon.awssdk:s3:$awsSdkVersion")

    // Open AI
    implementation("com.aallam.openai:openai-client:$openAIVersion")
    implementation("io.ktor:ktor-client-java:$ktorClientJavaVersion")

    // Caffeine
    implementation("com.github.ben-manes.caffeine:caffeine:$caffeineVersion")

    // DynamoDB
    implementation("software.amazon.awssdk:dynamodb:$awsSdkVersion")
    implementation("software.amazon.awssdk:netty-nio-client:$awsSdkVersion")

    // Security
    implementation("com.keyway:kommons-auth0:$kommonsAuthVersion")
}

tasks.withType<KotlinCompile> {
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_17)
    }
}
