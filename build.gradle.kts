import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.jlleitschuh.gradle.ktlint.KtlintExtension

val slf4jVersion: String by rootProject
val ktlintVersion: String by rootProject
val junitVersion: String by rootProject
val log4jVersion: String by rootProject
val micrometerVersion: String by rootProject
val kotlinxCoroutinesVersion: String by rootProject
val nettyVersion: String = "4.2.0.Final"

plugins {
    base
    kotlin("jvm") version "2.1.0"
    kotlin("kapt") version "2.1.0"
    idea
    jacoco
    application
    id("org.jlleitschuh.gradle.ktlint") version "12.1.2"
}

buildscript {
    dependencies {
        classpath(kotlin("gradle-plugin", version = "2.1.0"))
        classpath(kotlin("serialization", version = "2.1.0"))
    }
}

allprojects {
    group = "com.keyway"
    version = "1.0.0"

    apply(plugin = "kotlin")

    configurations.all {
        resolutionStrategy {
            force("ch.qos.logback:logback-classic:1.5.18")
            force("ch.qos.logback:logback-core:1.5.18")
            force("io.netty:netty-handler:$nettyVersion")
            force("io.netty:netty-common:$nettyVersion")
            force("io.netty:netty-codec:$nettyVersion")
            force("io.netty:netty-codec-http2:$nettyVersion")
            force("com.google.guava:guava:33.4.8-android")
            force("commons-io:commons-io:2.19.0")
        }
    }

    repositories {
        mavenCentral()
        listOf("kommons-mapper", "kommons-aws", "kommons-auth0")
            .forEach { projectId ->
                maven {
                    url = uri("https://maven.pkg.github.com/unlockre/$projectId")
                    credentials {
                        username = project.findProperty("gpr.user") as String? ?: System.getenv("GH_USERNAME")
                        password = project.findProperty("gpr.key") as String? ?: System.getenv("GH_TOKEN")
                    }
                }
            }
    }
}

subprojects {

    dependencies {

        // Log
        implementation("org.apache.logging.log4j:log4j-api:$log4jVersion")
        implementation("org.apache.logging.log4j:log4j-core:$log4jVersion")
        implementation("org.apache.logging.log4j:log4j-slf4j2-impl:$log4jVersion")

        // Metrics
        implementation("io.micrometer:micrometer-registry-datadog:$micrometerVersion")

        // Kotlinx coroutines
        implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlinxCoroutinesVersion")
        implementation("org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:$kotlinxCoroutinesVersion")
    }

    tasks.test {
        useJUnitPlatform()
        testLogging {
            lifecycle {
                events =
                    mutableSetOf(
                        org.gradle.api.tasks.testing.logging.TestLogEvent.FAILED,
                        org.gradle.api.tasks.testing.logging.TestLogEvent.PASSED,
                        org.gradle.api.tasks.testing.logging.TestLogEvent.SKIPPED,
                    )
                exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
                showExceptions = true
                showCauses = true
                showStackTraces = true
                showStandardStreams = true
            }
            info.events = lifecycle.events
            info.exceptionFormat = lifecycle.exceptionFormat
        }

        addTestListener(
            object : TestListener {
                override fun beforeSuite(suite: TestDescriptor) {}

                override fun beforeTest(testDescriptor: TestDescriptor) {}

                override fun afterTest(
                    testDescriptor: TestDescriptor,
                    result: TestResult,
                ) {}

                override fun afterSuite(
                    suite: TestDescriptor,
                    result: TestResult,
                ) {
                    if (suite.parent == null) { // root suite
                        logger.lifecycle("----")
                        logger.lifecycle("Test result: ${result.resultType}")
                        logger.lifecycle(
                            "Test summary: ${result.testCount} tests, " +
                                "${result.successfulTestCount} succeeded, " +
                                "${result.failedTestCount} failed, " +
                                "${result.skippedTestCount} skipped",
                        )
                    }
                }
            },
        )
    }

    tasks.withType<KotlinCompile> {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
    }

    tasks.register<Copy>("installGitHook") {
        from("$rootDir/scripts/pre-commit")
        into("$rootDir/.git/hooks/.")
        fileMode = Integer.parseInt("0777", 8)
    }
    tasks.getByName("assemble").dependsOn(tasks.named("installGitHook"))
}

configure<KtlintExtension> {
    version.set(ktlintVersion)
}
