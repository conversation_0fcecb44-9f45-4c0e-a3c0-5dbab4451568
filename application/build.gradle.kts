import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar
import org.jetbrains.kotlin.gradle.dsl.JvmTarget

val koinVersion: String by rootProject
val javalinVersion: String by rootProject
val jacksonVersion: String by rootProject
val commonsLangVersion: String by rootProject
val kommonsMapperVersion: String by rootProject
val unirestVersion: String by rootProject
val kommonsDbVersion: String by rootProject
val kommonsHttpVersion: String by rootProject
val awsSdkVersion: String by rootProject
val openAIVersion: String by rootProject
val ktorClientJavaVersion: String by rootProject
val caffeineVersion: String by rootProject
val kommonsAwsVersion: String by rootProject
val swaggerParserVersion: String by rootProject
val jettyVersion: String by rootProject
val kommonsAuthVersion: String by rootProject
val auth0Version: String by rootProject

// Application
val mainClassFqn: String = "com.keyway.application.Application"

application {
    mainClass.set(mainClassFqn)
}

plugins {
    kotlin("jvm")
    kotlin("kapt")
    application
    id("com.gradleup.shadow") version "8.3.5"
    kotlin( "plugin.serialization")
}

dependencies {
    implementation(project(":core"))
    implementation(project(":adapters"))


    implementation("io.javalin:javalin:$javalinVersion")
    kapt("io.javalin.community.openapi:openapi-annotation-processor:$javalinVersion")
    implementation("io.javalin.community.openapi:javalin-openapi-plugin:$javalinVersion")
    implementation("io.javalin.community.openapi:javalin-swagger-plugin:$javalinVersion")


    implementation("com.keyway:kommons-aws:$kommonsAwsVersion")

    // IoC
    // Koin Core features
    implementation("io.insert-koin:koin-core:$koinVersion")

    // Mapper
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:$jacksonVersion")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jacksonVersion")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:$jacksonVersion")
    implementation("com.fasterxml.jackson.core:jackson-databind:$jacksonVersion")
    implementation("com.keyway:kommons-mapper:$kommonsMapperVersion") {
        exclude(group = "com.google.code.gson")
    }

    // Utils
    implementation("org.apache.commons:commons-lang3:$commonsLangVersion")

    // Rest
    implementation("com.konghq:unirest-java:$unirestVersion")
    implementation("com.keyway:kommons-http:$kommonsHttpVersion"){
        exclude(group = "org.apache.logging.log4j")
    }

    // S3
    implementation("software.amazon.awssdk:s3:$awsSdkVersion")

    // Open AI
    implementation("com.aallam.openai:openai-client:$openAIVersion")
    implementation("io.ktor:ktor-client-java:$ktorClientJavaVersion")

    // Caffeine
    implementation("com.github.ben-manes.caffeine:caffeine:$caffeineVersion")

    // DynamoDB
    implementation("software.amazon.awssdk:dynamodb:$awsSdkVersion")
    implementation("software.amazon.awssdk:netty-nio-client:$awsSdkVersion")

    // Security
    implementation("com.keyway:kommons-auth0:$kommonsAuthVersion")
    implementation("com.auth0:jwks-rsa:$auth0Version")
}

tasks {

    withType<KotlinCompile> {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
            freeCompilerArgs.set(listOf("-Xopt-in=kotlin.RequiresOptIn"))
        }
    }

    named<ShadowJar>("shadowJar") {
        archiveBaseName.set("service")
        archiveClassifier.set("")
        archiveVersion.set("")
        manifest {
            attributes(mapOf("Main-Class" to application.mainClass.get()))
        }
    }

    named<JavaExec>("run") {
        doFirst {
            args = listOf("run")
        }
    }
}
