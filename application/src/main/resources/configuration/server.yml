service_name: "key-assist-api"

system:
  api_url: "${API_URL}"
  http_port: "8080"
  timeout: 10000

rest_clients:
  - name: "data-gpt"
    host: "${DATA_GPT_API_URL}"
    default_connection_timeout: "${DATA_GPT_CONNECTION_TIMEOUT_CONFIG}"
    default_socket_timeout: "${DATA_GPT_SOCKET_TIMEOUT_CONFIG}"
    client_params:
      auth_token: "${QUERY_BY_PROPERTY_ID_AUTH_TOKEN}"

aws_config:
  region: "us-east-1"
  account_id: "************"
  access_key: "${AWS_ACCESS_KEY}"
  secret_key: "${AWS_SECRET_KEY}"

s3_config:
  force_path_style: false
  buckets:
    - key: key-assist
      name: "${KEY_ASSIST_BUCKET_NAME}"

dynamo_db_config:
  tables:
    - key: sessions
      name: "${KEY_ASSIST_SESSIONS_TABLE_NAME}"
      timeout: 2000

datadog_config:
  step_in_seconds: 20
  api_key: "${DATADOG_API_KEY}"

open_ai_config:
  token: "${OPEN_AI_TOKEN}"
  timeout: 60000

security:
  enabled: "${SECURITY_ENABLED}"
  issuer: "${AUTH0_ISSUER}"
  domain: "${AUTH0_DOMAIN}"
  audiences: ${AUTH0_AUDIENCES}
  keyway_organization_id: "${KEYWAY_ORGANIZATION_ID}"