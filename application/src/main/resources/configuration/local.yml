service_name: "key-assist-api"

system:
  api_url: "http://localhost:8080"
  http_port: "8080"
  timeout: 10000

rest_clients:
  - name: "data-gpt"
    host: "https://data-gpt-api-dev-fcrgmv73aq-uc.a.run.app"
    default_connection_timeout: "10000"
    default_socket_timeout: "50000"
    client_params:
      auth_token: "query_auth_token"

aws_config:
  region: "us-east-1"
  account_id: "************"
  access_key: "access"
  secret_key: "secret"
  endpoint_override: "http://localstack:4566"

s3_config:
  force_path_style: true
  buckets:
    - key: key-assist
      name: key-assist-local

dynamo_db_config:
  tables:
    - key: sessions
      name: key-assist_sessions_local
      timeout: 2000

datadog_config:
  step_in_seconds: 20
  api_key: "datadog_api_key"

open_ai_config:
  token: "token"
  timeout: 60000

security:
  enabled: true
  issuer: "whykeyway.com"
  domain: "whykeyway.com"
  audiences: ["whykeyway.com"]
  keyway_organization_id: "keyway"