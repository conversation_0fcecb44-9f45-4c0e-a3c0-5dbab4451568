import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

val koinVersion: String by rootProject
val junitVersion: String by rootProject
val unirestVersion: String by rootProject
val javalinVersion: String by rootProject
val kommonsMapperVersion: String by rootProject
val kommonsHttpVersion: String by rootProject
val mockkVersion: String by rootProject
val caffeineVersion: String by rootProject
val hamcrestVersion: String by rootProject
val hamcrestDateVersion: String by rootProject
val awsSdkVersion: String by rootProject
val kommonsAuthVersion: String by rootProject

dependencies {
    testImplementation(project(":adapters"))
    testImplementation(project(":application"))
    testImplementation(project(":core"))

    // Javalin
    testImplementation("io.javalin:javalin:$javalinVersion")

    // Mapper
    testImplementation("com.keyway:kommons-mapper:$kommonsMapperVersion") {
        exclude(group = "com.google.code.gson")
    }

    // Rest
    testImplementation("com.konghq:unirest-objectmapper-jackson:$unirestVersion")
    testImplementation("com.konghq:unirest-java:$unirestVersion")
    testImplementation("com.keyway:kommons-http:$kommonsHttpVersion") {
        exclude(group = "org.apache.logging.log4j")
    }

    // S3
    testImplementation("software.amazon.awssdk:s3:$awsSdkVersion")

    // Caffeine
    implementation("com.github.ben-manes.caffeine:caffeine:$caffeineVersion")

    // DynamoDB
    implementation("software.amazon.awssdk:dynamodb:$awsSdkVersion")
    implementation("software.amazon.awssdk:netty-nio-client:$awsSdkVersion")

    // Security
    testImplementation("com.keyway:kommons-auth0:$kommonsAuthVersion")

    // Test
    testImplementation("io.insert-koin:koin-test:$koinVersion")
    testImplementation("io.insert-koin:koin-test-junit5:$koinVersion")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine:$junitVersion")
    testImplementation("org.junit.jupiter:junit-jupiter-api:$junitVersion")
    testImplementation("org.junit.jupiter:junit-jupiter-params:$junitVersion")
    testImplementation("io.mockk:mockk:$mockkVersion")
    testImplementation("org.hamcrest:hamcrest-library:$hamcrestVersion")
    testImplementation("org.exparity:hamcrest-date:$hamcrestDateVersion")
}

tasks.withType<KotlinCompile> {
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_17)
    }
}
