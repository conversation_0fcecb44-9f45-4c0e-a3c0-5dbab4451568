## This PR
[Provide a brief description of the changes introduced in this pull request.]

## Related Tickets
[Cite any related issues or feature requests that this pull request addresses.]

## Proposed Changes
[List the specific changes and additions made in this pull request.]

## External Dependencies
- **Environment Variables**: Document any new or updated environment variables required for the changes introduced in this PR. Specify their purpose and, if applicable, default values.
- **Database Tasks**: If there are any new database scripts or modifications, outline the purpose and include the necessary steps for executing them.
- **Other services**: If there are any feature from other microservice required.

## Additional Notes
[Include any additional information, context, or instructions for the reviewer.]

## Checklist
- [ ] Open API Documentation has been updated, if applicable
- [ ] Environment variables and database scripts have been reviewed
- [ ] External feature dependencies have been reviewed
