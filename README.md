## Requires
* Java 17
* Kotlin 1.6.X
* Gradle 7.X

# Beginning steps
## Gradle Setup (Build)
For <PERSON><PERSON><PERSON> to be able to download our packages, you will need to setup two environment variables:
- GH_USERNAME: Your github username
- GH_TOKEN: Your github token

## Project Assemble
To ensure the proper functioning of the application make sure to run 
- gradle task: assemble

## Project Structure
The project has the following modules structure:

- application: acts as the delivery lawyer, only responsible to expose the app (in this case) as a ktor REST API, including the configuration for the netty server and the endpoint routing. 
- adapters: connects the usecases with the external world, it contains the repository implementations, http clients, controllers and response mappers.
- usescases: every use case receives a request and returns a response, it contains the businness logic of our domain.
- core: only contains the domain entities

## Project Configs
- Socket time out for rest integrations is customizable by environment in this project

## Architecture diagram
![architecture](statics/clean_arch.png)

## API Docs (DEV)
- OpenAPI: https://key-assist-api.dev.whykeyway.com/api/docs
- Swagger: https://key-assist-api.dev.whykeyway.com/swagger