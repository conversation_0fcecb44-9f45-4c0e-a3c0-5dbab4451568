version: '3'

services:
  test:
    image: amazoncorretto:17-alpine
    platform: linux/amd64
    depends_on:
      localstack:
        condition: service_healthy
    volumes:
      - .:/ws
    working_dir: /ws
    environment:
      GH_USERNAME: ${GH_USERNAME}
      GH_TOKEN: ${GH_TOKEN}
    command: >
      ./gradlew --no-daemon -g ./.gradle-cache ktlintCheck test --info

  localstack:
    container_name: "localstack_key_assist_api"
    image: localstack/localstack:3.5.0
    ports:
      - "4566:4566"
      - "4571:4571"
    environment:
      - AWS_DEFAULT_REGION=us-east-1
      - SERVICES=s3,dynamodb
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - "${TMPDIR:-/tmp}/localstack:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "./aws_local_resources.sh:/etc/localstack/init/ready.d/aws_local_resources.sh"
    healthcheck:
      test: [ "CMD", "bash", "-c", "awslocal dynamodb list-tables" ]
      interval: 5s
      timeout: 10s
      retries: 30
      start_period: 10s