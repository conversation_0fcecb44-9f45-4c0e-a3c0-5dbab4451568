import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

val kommonsMapperVersion: String by rootProject
val jacksonVersion: String by rootProject

plugins {
    kotlin("jvm")
}

dependencies {
    // Mapper
    implementation("com.fasterxml.jackson.core:jackson-databind:$jacksonVersion")
    implementation("com.keyway:kommons-mapper:$kommonsMapperVersion") {
        exclude(group = "com.google.code.gson")
    }
}

tasks.withType<KotlinCompile> {
    kotlinOptions.jvmTarget = JavaVersion.VERSION_17.toString()
}
